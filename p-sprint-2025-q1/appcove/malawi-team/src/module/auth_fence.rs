use crate::{AppStruct, IdentityStruct};

impl auth_fence::Identity for IdentityStruct {
    fn is_logged_in(&self) -> bool {
        self.auth_fence.is_some()
    }
    fn identity_uuid(&self) -> Option<granite::Uuid> {
        self.auth_fence
            .as_ref()
            .map(|auth_fence| auth_fence.identity_uuid)
    }
    fn remote_address(&self) -> std::net::IpAddr {
        self.request.remote_address
    }
    fn session_token(&self) -> String {
        self.request.session_token.clone()
    }
}

impl auth_fence::App for AppStruct {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/dashboard/",
        }
    }
    fn auth_fence_system(&self) -> &auth_fence::types::ModuleStruct {
        &self.auth_fence
    }
}
