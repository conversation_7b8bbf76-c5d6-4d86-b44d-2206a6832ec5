#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Malawi Team");

        doc.add_body(html!(
            div.container.bg-white {
                h1 { "Welcome to Malawi Team" }

                p { "This is the Malawi Team application homepage." }

                hr;

                .row {
                    .col-md-6 {
                        h2 { "Features" }
                        ul {
                            li { "Team Management" }
                            li { "Project Coordination" }
                            li { "Database Integration" }
                            li { "Google Authentication" }
                        }
                    }
                    .col-md-6 {
                        h2 { "Get Started" }
                        p { "Sign in with your Google account to access the dashboard and start managing your team." }
                        // Auth-fence will automatically add login button here
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
