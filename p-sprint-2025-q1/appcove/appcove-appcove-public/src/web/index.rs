#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("AppCove - At a Glance");

        doc.add_body(html!(
            div.container-fluid.p-0 style="background-color: #f5f5f5; min-height: 100vh;" {
                div.container.py-5 {
                    div.row.g-4 {
                        // Left Column - Company Info with Avatar
                        div.col-lg-3 {
                            div.text-center.mb-4 {
                                // Avatar circle
                                div.avatar-circle style="width: 200px; height: 200px; background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;" {
                                    // Professional avatar illustration
                                    div style="width: 120px; height: 120px; background: #4a9eff; border-radius: 50%; position: relative;" {
                                        // Head
                                        div style="width: 60px; height: 60px; background: #fdbcb4; border-radius: 50%; position: absolute; top: 20px; left: 30px;" {}
                                        // Body/Suit
                                        div style="width: 80px; height: 40px; background: #2c5aa0; position: absolute; bottom: 20px; left: 20px; border-radius: 10px 10px 0 0;" {}
                                        // Tie
                                        div style="width: 8px; height: 25px; background: #1a1a1a; position: absolute; bottom: 35px; left: 56px;" {}
                                    }
                                }
                            }

                            h1.display-4.fw-bold.text-primary.mb-3 { "AppCove" }

                            h5.text-info.mb-4 {
                                "We build custom software focused on providing "
                                strong { "your customers with a premium experience." }
                            }

                            p.text-muted.mb-4 {
                                "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. We take your long term business goals seriously."
                            }

                            p.small.text-muted.mb-4 {
                                "This is reflected in every aspect of our work — from our people and training to our software and contracts. While you focus on innovation and operations, we focus on delivering technical excellence, robust security, and flexible infrastructure — whether hosted with us or deployed on your cloud."
                            }

                            // Company Details
                            div.company-info {
                                p.mb-2 { strong.text-primary { "FOUNDED " } "2003" }
                                p.mb-2 { strong.text-primary { "OWNER " } "Jason Garber" }
                                p.mb-2 { strong.text-primary { "SIZE " } "20+ Employees" }
                                p.mb-4 { strong.text-primary { "HEADQUARTERS " } "Altoona, PA" }
                            }
                        }

                        // Center Column - Problems & Answers
                        div.col-lg-6 {
                            div.row.mb-4 {
                                div.col-6 {
                                    h2.text-primary.fw-bold { "Problems" }
                                    hr style="border-top: 2px dashed #6c757d; width: 100%;"
                                }
                                div.col-6 {
                                    h2.text-primary.fw-bold { "Answers" }
                                }
                            }

                            // Problem-Answer pairs
                            div.problem-answer-section {
                                // Pair 1
                                div.row.mb-4.align-items-center {
                                    div.col-5 {
                                        p.text-muted.small {
                                            "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other."
                                        }
                                    }
                                    div.col-2.text-center {
                                        div.arrow-circle style="width: 40px; height: 40px; background: #17a2b8; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;" {
                                            span.text-white.fw-bold { "→" }
                                        }
                                    }
                                    div.col-5 {
                                        p.text-muted.small {
                                            "AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place."
                                        }
                                    }
                                }

                                // Pair 2
                                div.row.mb-4.align-items-center {
                                    div.col-5 {
                                        p.text-muted.small {
                                            "Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools."
                                        }
                                    }
                                    div.col-2.text-center {
                                        div.arrow-circle style="width: 40px; height: 40px; background: #17a2b8; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;" {
                                            span.text-white.fw-bold { "→" }
                                        }
                                    }
                                    div.col-5 {
                                        p.text-muted.small {
                                            "AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users."
                                        }
                                    }
                                }
                            }
                        }

                        // Right Column - Timeline
                        div.col-lg-3 {
                            div.timeline-header.mb-4 {
                                h3.text-primary.fw-bold { "Custom Software" }
                                h4.text-primary.fw-bold { "Deployment Timeline" }
                                p.text-info.small.fw-bold { "A SELECTION OF NOTABLE PROJECTS" }
                            }

                            div.timeline {
                                // 2004 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #17a2b8; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong.text-info { "2004 — 21 Years " }
                                                span.badge.bg-success.text-white.small { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 { "Advanced Reservation System" }
                                            p.text-muted.small {
                                                "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for an exclusive Pennsylvania Fishing club."
                                            }
                                        }
                                    }
                                }

                                // 2005 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #6c757d; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong.text-muted { "2005 — 15 Years " }
                                                span.badge.bg-secondary.text-white.small { "Inactive Project" }
                                            }
                                            h6.fw-bold.mb-2 { "CRM & Marketing Software" }
                                            p.text-muted.small {
                                                "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM."
                                            }
                                        }
                                    }
                                }

                                // 2009 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #17a2b8; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong.text-info { "2009 — 16 Years " }
                                                span.badge.bg-success.text-white.small { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 { "Client Marketing Dashboard" }
                                            p.text-muted.small {
                                                "Built a custom marketing planning, community, and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses."
                                            }
                                        }
                                    }
                                }

                                // 2013 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #17a2b8; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong.text-info { "2013 — 12 Years " }
                                                span.badge.bg-success.text-white.small { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 { "Technical Investment in ACRM" }
                                            p.text-muted.small {
                                                "Engineered and implemented a set of foundational modules that became the basis for all approck software going forward."
                                            }
                                        }
                                    }
                                }

                                // 2016 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #17a2b8; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong.text-info { "2016 — 9 Years " }
                                                span.badge.bg-success.text-white.small { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 { "Dashboard as a Service" }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
