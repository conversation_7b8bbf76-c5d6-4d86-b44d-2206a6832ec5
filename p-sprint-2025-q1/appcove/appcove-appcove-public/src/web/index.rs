
#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page{
pub async fn request(doc: Document) -> Response {
use maud::html;

    doc.set_title("AppCove");

    doc.add_body(html!(
        div.container.mt-4 {
            div.row {
                div.col-12 {
                    h1 { "AppCove, Inc." }
                    p { "Welcome to AppCove" }
                    p {"We build custom software focused on providing customers your customer with a premium experience"}
                    p{"AppCove has a proven track record of building and supporting reliable web applications for over 20 years. We take your long term business goals seriously."} 
                    p{"This is reflected in every aspect of our work — from our people and training to our software and contracts. While you focus on innovation and operations, we focus on delivering technical excellence, robust security, and flexible infrastructure — whether hosted with us or deployed on your cloud."}

                    hr;
                    // Company Info 
                    p {"Company Information"}
                    p {"FOUNDED 2003"}
                    p {"OWNER Jason Garber"}
                    p {"SIZE 20+Employees"}
                    p {"HEADQUARTERS Altoona, PA"}

                     hr;
                     //problem-Answer Section 
                    p {"Problems & Answers"}
                    p {"Business Data is contained in a growing number of SaaS providers that do not communicate well with each other."}
                    p {"AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place."}

                    p {"Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools."}
                    p {"AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users."}

                    p {"Custom software can be slow to build, with uncertain outcomes, and may lack long term support."}
                    p {"AppCove's pursuit of technical excellence, coupled with a willingness to learn the details of your business results in efficient code, streamlined implementation, and a custom software product built for long-term support."}

                    p {"Artificial intelligence fails to maximize value if it does not have access to reliable, structured business data."}
                    p {"AppCove's rigorous database design approach positions each client ideally to participate in the rapid increase of AI capabilities."}

                    hr;
                    //staff
                    p {"Key Staff"}
                    p {"Jason Garber - President & Architect - 22 YEARS WITH APPCOVE"}
                    p {"Julie Garber - Director of Operations"}
                    p {"Andrew Bidochko - Senior Engineer - 21 YEARS WITH APPCOVE"}
                    p {"Sergio Olivo - Director of Engineering"}
                    p {"John Koval - Director of Security"}

                    hr;
                    section {
                    panel {
                    content {
                        // Contact Information 
                    tr {
                    th{"FOR BUSINESS INQUIRES, CONTACT"}
                      }
                    tr{
                    p {"Jason Garber"}
                    p {"<EMAIL>"}
                    p {"(814) 240-3338"}
                    p {"P.O. Box 1309"}
                    p {"Altoona PA 16603"}
                      }
                    hr;
                    //Right Side-Timeline
                    p {"Custom Software Deployment Timeline"}
                    p {"A SELECTION OF NOTABLE PROJECTS"}

                    hr;
                    p {"2004 — 21 Years"}
                    p {"(Active Project)"}
                    p {"Advanced Reservation System"}
                    p {"Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for an exclusive Pennsylvania Fishing club."}
                    p {"2005 — 15 Years"}
                    p {"(Inactive Project)"}
                    p {"CRM & Marketing Software"}
                    p {"Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM"}
                    p {"2009 — 16 Years"}
                    p {"(Active Project)"}
                    p {"Client Marketing Dashboard"}
                    p {"Built a custom marketing planning, community, and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses."}
                    p {"2013 — 12 Years"}
                    p {"(Active Project)"}
                    p {"Technical Investment in ACRM"}
                    p {"Engineered and implemented a set of foundational modules that became the basis for all AppCove software going forward."}
                    p {"2016 — 9 Years"}
                    p {"(Active Project)"}
                    p {"Dashboard as a Service"}
                    p {"Designed a platform used by coaching organizations to serve their clients with content, training, community, and communications. Used by successful organizations in areas such as IT, Trades, Finance, and Law."}
                    p {"2016 - 9 Years"}
                    p {"Active Project"}
                    p {"Tools for Financial Advisors"}
                    p {"Created custom software representing unique finance and investment products. Hundreds of financial advisors have used these to serve tens of thousands of clients."}
                    p {"2018 — 7 Years"}
                    p {"(Active Project)"}
                    p {"Technical Investment in ACE"}
                    p {"Architected the AppCove Cloud Engine to consolidate cloud management, security, BDR, and deployment of custom software."}
                    p {"2020 - 5 Years"}
                    p {"Active Project"}
                    p {"Virtual Event Platform"}
                    p {"Deployed a scalable virtual event platform with custom graphical themes, live session streaming, interactive chat, video chat, help desk, breakout rooms, sponsor booths, broadcast studio, pre-recorded content, gamification, and real-time notifications."}
                    p {"2023"}
                    p {"(Active Project)"}
                    p {"Technical Investment in ACE 2.0"}
                    p {"ACE 2.0 is designed to cut through the complexity of the cloud, and deliver stable infrastructure to all custom applications we build going forward."}
                    p {"2025"}
                    p {"Active Project"}
                    p {"Technical Investment in approck"}
                    p {"Engineered and implemented our next generation toolbox of software infrastructure, dev/ops, and database management tools."}
                    p {"2025"}
                    p {"(Active Project)"}
                    p {"Fin/Tech Products"}
                    p {"Implementing next generation financial products combining credit reporting, insurance underwriting, debt payoff plans, community, and communications to be used by hundreds of financial professionals in assisting their clients."}
                    }
                    }
                    }
                }
            }
        }
    ));

    Response::HTML(doc.into())
}