#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("AppCove - At a Glance");

        doc.add_body(html!(
            div.container-fluid.p-0 style="background-color: #f5f5f5; min-height: 100vh; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;" {
                div.container.py-5 {
                    div.row.g-4 {
                        // Left Column - Company Info with Avatar
                        div.col-lg-4 {
                            div.text-center.mb-4 {
                                // Avatar circle with professional illustration
                                div.avatar-circle style="width: 200px; height: 200px; background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;" {
                                    // Professional avatar illustration matching the image
                                    div style="width: 120px; height: 120px; position: relative;" {
                                        // Head
                                        div style="width: 50px; height: 50px; background: #fdbcb4; border-radius: 50%; position: absolute; top: 15px; left: 35px;" {}
                                        // Hair
                                        div style="width: 60px; height: 30px; background: #4a4a4a; border-radius: 30px 30px 0 0; position: absolute; top: 10px; left: 30px;" {}
                                        // Body/Suit - Green jacket
                                        div style="width: 80px; height: 50px; background: #20b2aa; position: absolute; bottom: 10px; left: 20px; border-radius: 15px 15px 0 0;" {}
                                        // White shirt
                                        div style="width: 20px; height: 35px; background: white; position: absolute; bottom: 25px; left: 50px;" {}
                                        // Black tie
                                        div style="width: 8px; height: 25px; background: #1a1a1a; position: absolute; bottom: 25px; left: 56px;" {}
                                    }
                                }
                            }

                            h1.display-4.fw-bold style="color: #2c5aa0; font-size: 3.5rem; margin-bottom: 1rem;" { "AppCove" }

                            h5 style="color: #20b2aa; font-weight: 600; margin-bottom: 1.5rem; line-height: 1.4;" {
                                "We build custom software focused on providing "
                                strong { "your customers with a premium experience." }
                            }

                            p style="color: #6c757d; margin-bottom: 1.5rem; line-height: 1.6;" {
                                "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. We take your long term business goals seriously."
                            }

                            p.small style="color: #6c757d; margin-bottom: 2rem; line-height: 1.5;" {
                                "This is reflected in every aspect of our work — from our people and training to our software and contracts. While you focus on innovation and operations, we focus on delivering technical excellence, robust security, and flexible infrastructure — whether hosted with us or deployed on your cloud."
                            }

                            // Company Details
                            div.company-info {
                                p.mb-2 style="color: #2c5aa0; font-weight: 600;" { "FOUNDED " span style="color: #333; font-weight: normal;" { "2003" } }
                                p.mb-2 style="color: #2c5aa0; font-weight: 600;" { "OWNER " span style="color: #333; font-weight: normal;" { "Jason Garber" } }
                                p.mb-2 style="color: #2c5aa0; font-weight: 600;" { "SIZE " span style="color: #333; font-weight: normal;" { "20+ Employees" } }
                                p.mb-4 style="color: #2c5aa0; font-weight: 600;" { "HEADQUARTERS " span style="color: #333; font-weight: normal;" { "Altoona, PA" } }
                            }
                        }

                        // Center Column - Problems & Answers
                        div.col-lg-4 {
                            div.row.mb-4 {
                                div.col-6 {
                                    h2 style="color: #2c5aa0; font-weight: bold; font-size: 2rem;" { "Problems" }
                                }
                                div.col-6 {
                                    h2 style="color: #2c5aa0; font-weight: bold; font-size: 2rem;" { "Answers" }
                                }
                            }
                            hr style="border-top: 2px dashed #6c757d; width: 100%; margin-bottom: 2rem;"

                            // Problem-Answer pairs
                            div.problem-answer-section {
                                // Pair 1
                                div.row.mb-4.align-items-center {
                                    div.col-5 {
                                        p style="color: #6c757d; font-size: 0.9rem; line-height: 1.4;" {
                                            "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other."
                                        }
                                    }
                                    div.col-2.text-center {
                                        div.arrow-circle style="width: 40px; height: 40px; background: #20b2aa; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;" {
                                            span.text-white.fw-bold style="font-size: 1.2rem;" { "→" }
                                        }
                                    }
                                    div.col-5 {
                                        p style="color: #6c757d; font-size: 0.9rem; line-height: 1.4;" {
                                            "AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place."
                                        }
                                    }
                                }

                                // Pair 2
                                div.row.mb-5.align-items-center {
                                    div.col-5 {
                                        p style="color: #6c757d; font-size: 0.9rem; line-height: 1.4;" {
                                            "Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools."
                                        }
                                    }
                                    div.col-2.text-center {
                                        div.arrow-circle style="width: 40px; height: 40px; background: #20b2aa; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;" {
                                            span.text-white.fw-bold style="font-size: 1.2rem;" { "→" }
                                        }
                                    }
                                    div.col-5 {
                                        p style="color: #6c757d; font-size: 0.9rem; line-height: 1.4;" {
                                            "AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users."
                                        }
                                    }
                                }
                            }
                        }

                        // Right Column - Timeline
                        div.col-lg-4 {
                            div.timeline-header.mb-4 {
                                h3 style="color: #2c5aa0; font-weight: bold; font-size: 1.8rem; margin-bottom: 0.5rem;" { "Custom Software" }
                                h4 style="color: #2c5aa0; font-weight: bold; font-size: 1.5rem; margin-bottom: 0.5rem;" { "Deployment Timeline" }
                                p style="color: #20b2aa; font-size: 0.8rem; font-weight: bold; letter-spacing: 0.5px;" { "A SELECTION OF NOTABLE PROJECTS" }
                            }

                            div.timeline {
                                // 2004 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2004 — 21 Years " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Advanced Reservation System" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for an exclusive Pennsylvania Fishing club."
                                            }
                                        }
                                    }
                                }

                                // 2005 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #6c757d; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #6c757d;" { "2005 — 15 Years " }
                                                span.badge style="background-color: #6c757d; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Inactive Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "CRM & Marketing Software" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM."
                                            }
                                        }
                                    }
                                }

                                // 2009 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2009 — 16 Years " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Client Marketing Dashboard" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Built a custom marketing planning, community, and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses."
                                            }
                                        }
                                    }
                                }

                                // 2013 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2013 — 12 Years " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Technical Investment in ACRM" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Engineered and implemented a set of foundational modules that became the basis for all approck software going forward."
                                            }
                                        }
                                    }
                                }

                                // 2016 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2016 — 9 Years " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Dashboard as a Service" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Engine to consolidate cloud management, security, BDR, and deployment of custom software."
                                            }
                                        }
                                    }
                                }

                                // 2020 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2020 — 5 Years " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Virtual Event Platform" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Deployed a scalable virtual event platform with custom graphical themes, live session streaming, interactive chat, video chat, help desk, breakout rooms, sponsor booths, broadcast studio, pre-recorded content, gamification, and real-time notifications."
                                            }
                                        }
                                    }
                                }

                                // 2023 Project
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2023 " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Technical Investment in ACE 2.0" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "ACE 2.0 is designed to cut through the complexity of the cloud, and deliver stable infrastructure to all custom applications we build going forward."
                                            }
                                        }
                                    }
                                }

                                // 2025 Projects
                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2025 " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Technical Investment in approck" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Engineered and implemented our next generation toolbox of software infrastructure, devOps, and database management tools."
                                            }
                                        }
                                    }
                                }

                                div.timeline-item.mb-4 {
                                    div.d-flex.align-items-start {
                                        div.timeline-dot style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-top: 5px; margin-right: 15px; flex-shrink: 0;" {}
                                        div {
                                            p.mb-1 {
                                                strong style="color: #20b2aa;" { "2025 " }
                                                span.badge style="background-color: #28a745; color: white; font-size: 0.7rem; padding: 2px 6px;" { "Active Project" }
                                            }
                                            h6.fw-bold.mb-2 style="color: #333; font-size: 1rem;" { "Fin/Tech Products" }
                                            p style="color: #6c757d; font-size: 0.85rem; line-height: 1.4;" {
                                                "Implementing next generation financial products combining credit reporting, insurance underwriting, debt payoff plans, community, and communications to be used by hundreds of financial professionals in assisting their clients."
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Second Row - Key Staff Section
                    div.row.mt-5 {
                        div.col-lg-8.offset-lg-4 {
                            h3 style="color: #20b2aa; font-weight: bold; font-size: 1.8rem; margin-bottom: 2rem;" { "Key Staff" }

                            div.row.g-3 {
                                // Row 1
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Jason Garber" }
                                        p.text-muted.small.mb-1 { "President & Architect" }
                                        p style="color: #20b2aa; font-size: 0.8rem; font-weight: bold;" { "22 YEARS WITH APPCOVE" }
                                    }
                                }
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Julie Garber" }
                                        p.text-muted.small.mb-1 { "Director of Operations" }
                                    }
                                }

                                // Row 2
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Andrew Bidochko" }
                                        p.text-muted.small.mb-1 { "Senior Engineer" }
                                        p style="color: #20b2aa; font-size: 0.8rem; font-weight: bold;" { "21 YEARS WITH APPCOVE" }
                                    }
                                }
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Sergio Olivo" }
                                        p.text-muted.small.mb-1 { "Technical Project Consultant" }
                                        p style="color: #20b2aa; font-size: 0.8rem; font-weight: bold;" { "19 YEARS WITH APPCOVE" }
                                    }
                                }

                                // Row 3
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Jeff Berdin" }
                                        p.text-muted.small.mb-1 { "Director of Infrastructure" }
                                    }
                                }
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Iryna Bidochko" }
                                        p.text-muted.small.mb-1 { "Software Engineer" }
                                        p style="color: #20b2aa; font-size: 0.8rem; font-weight: bold;" { "10 YEARS WITH APPCOVE" }
                                    }
                                }

                                // Row 4
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Jessi Garber" }
                                        p.text-muted.small.mb-1 { "Senior UI/UX Developer" }
                                        p style="color: #20b2aa; font-size: 0.8rem; font-weight: bold;" { "10 YEARS WITH APPCOVE" }
                                    }
                                }
                                div.col-6 {
                                    div.staff-card.p-3 style="background-color: #f8f9fa; border-radius: 8px;" {
                                        h6.fw-bold.mb-1 style="color: #333;" { "Don Berdin" }
                                        p.text-muted.small.mb-1 { "Director of Support" }
                                    }
                                }
                            }

                            // Contact Section
                            div.mt-5 {
                                h5 style="color: #2c5aa0; font-weight: bold; margin-bottom: 1.5rem;" { "FOR BUSINESS INQUIRES, CONTACT" }

                                div.row {
                                    div.col-6 {
                                        p.mb-1 { strong { "Jason Garber" } }
                                        p.text-muted.small.mb-1 { "<EMAIL>" }
                                        p.text-muted.small { "(814) 240-3338" }
                                    }
                                    div.col-6 {
                                        p.mb-1 { strong { "AppCove, Inc." } }
                                        p.text-muted.small.mb-1 { "P.O. Box 1309" }
                                        p.text-muted.small { "Altoona PA 16603" }
                                    }
                                }
                            }

                            // AppCove Logo
                            div.mt-5.text-start {
                                h2 style="color: #2c5aa0; font-weight: bold; font-size: 2.5rem;" { "AppCove" }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
