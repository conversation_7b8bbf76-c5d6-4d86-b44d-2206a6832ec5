bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            _app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            use bux::document::Cliffy;
            use bux::document::{Base, Nav2};
            use auth_fence::Identity as AuthIdentity;
            use bux::Identity as BuxIdentity;

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_title("AppCove"); // default title
            this.set_site_name("AppCove");
            this.set_owner("AppCove, Inc.");

            // Nav2 setup with authentication-aware navigation
            this.set_identity(identity);

            // Add main navigation
            this.add_nav2_menu_link("Dashboard", "/dashboard/", "dashboard");
            this.add_nav2_menu_link("About", "/about/", "about");

            // Add authentication-specific navigation
            if identity.is_logged_in() {
                // User is logged in - show user menu
                if let Some(name) = identity.name() {
                    this.add_nav2_menu_link(&format!("Welcome, {}", name), "#", "welcome");
                }
                this.add_nav2_menu_link("My Account", "/myaccount/", "myaccount");
                this.add_nav2_menu_link("Sign Out", "/auth/logout", "signout");
            } else {
                // User is not logged in - show sign in option
                this.add_nav2_menu_link("Sign In", "/auth/", "signin");
            }

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }

    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::Cliffy for Document {}
    impl auth_fence::Document for Document {}
}
