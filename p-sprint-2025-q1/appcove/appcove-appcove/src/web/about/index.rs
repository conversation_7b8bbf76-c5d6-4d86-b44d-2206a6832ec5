#[approck::http(GET /about/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(identity: Identity, doc: Document) -> Response {
        use maud::html;

        doc.set_title("About AppCove");

        doc.add_body(html! {
            div.container.mt-4 {
                div.row {
                    div.col-12 {
        h1 { "About AppCove" }

                        div.card.mt-4 {
                            div.card-header {
                                h5.card-title { "Project Overview" }
                            }
                            div.card-body {
                                p {
                                    "The AppCove application is a demonstration project built using the AppCove framework. "
                                    "It showcases modern web application development with Rust, featuring secure authentication, "
                                    "database integration, and a responsive user interface."
                                }

                                h6.mt-3 { "Key Technologies:" }
                                ul {
                                    li { strong { "Rust: " } "High-performance, memory-safe backend development" }
                                    li { strong { "AppRock Framework: " } "Modern web application framework for Rust" }
                                    li { strong { "Auth-Fence: " } "Secure OAuth2 authentication system" }
                                    li { strong { "PostgreSQL: " } "Robust relational database" }
                                    li { strong { "Redis: " } "High-performance caching layer" }
                                    li { strong { "Bootstrap: " } "Responsive CSS framework" }
                                    li { strong { "TypeScript: " } "Type-safe frontend development" }
                                }
                            }
                        }

                        div.card.mt-4 {
                            div.card-header {
                                h5.card-title { "Authentication Features" }
                            }
                            div.card-body {
                                p { "This application demonstrates comprehensive authentication capabilities:" }
                                ul {
                                    li { "🔐 Google OAuth2 integration" }
                                    li { "🔒 Secure session management" }
                                    li { "👤 User profile management" }
                                    li { "🛡️ Role-based access control" }
                                    li { "📱 Multi-device support" }
                                }

                                @if identity.is_logged_in() {
                                    div.alert.alert-success.mt-3 {
                                        strong { "✅ Authentication Status: " } "You are currently logged in!"
                                    }
                                } @else {
                                    div.alert.alert-info.mt-3 {
                                        strong { "ℹ️ Authentication Status: " }
                                        "You are not logged in. "
                                        a href="/auth/" { "Sign in here" }
                                        " to see personalized content."
                                    }
                                }
                            }
                        }

                        div.card.mt-4 {
                            div.card-header {
                                h5.card-title { "Development Information" }
                            }
                            div.card-body {
                                p { "This project follows AppCove conventions and patterns:" }
                                ul {
                                    li { "Modular architecture with clear separation of concerns" }
                                    li { "Type-safe API development with automatic code generation" }
                                    li { "Consistent naming conventions (snake_case for variables, CamelCase for types)" }
                                    li { "Integrated build system with hot reloading" }
                                    li { "Comprehensive error handling and logging" }
                                }

                                div.mt-3 {
                                    a.btn.btn-primary href="/dashboard/" { "Back to Dashboard" }
                                }
                            }
                        }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
