+ [36mappcove[m
* [32mappcove-sprint[m
  candoc[m
+ [36mmaster[m
  onesmus[m
  onesmus-test-branch[m
  p-sprint-2025-q1[m
  pm-acp7r-low-memory-configuration-10121667[m
  pm-appcove-training-onesmus-training-10120170[m
  pm5-Onesmus[m
  pmu-homewaters-hwc-mockups-homewaters-admin-mockups-10119651[m
  pmu-reo-project-reo-mockups-fix-unreadable-details-in-admin-client-screen-10120947[m
  pmu-reo-project-reo-mockups-make-email-info-match-10123453[m
  pmu-reo-project-reo-mockups-re-order-packet-list-10123452[m
  pmu-reo-project-reo-mockups-remove-employees-tab-10120958[m
  pmu-sprint-2025-q1-candoc-add-document-page-for-pathcap-10123995[m
  pmu-sprint-2025-q1-candoc-add-link-to-approck-example-in-app-dir-10123735[m
  pmu-sprint-2025-q1-candoc-add-link-to-log-page-10123530[m
  pmu-sprint-2025-q1-candoc-add-querystring-2-page-10125054[m
  pmu-sprint-2025-q1-candoc-add-querystring-2-page-10125101[m
  pmu-sprint-2025-q1-candoc-add-querystring-4-page-10125101[m
  pmu-sprint-2025-q1-candoc-add-querystring-page-10124653[m
  pmu-sprint-2025-q1-candoc-add-querystring-page-10124653-onesmus-test-p-sprint-candoc[m
  pmu-sprint-2025-q1-candoc-fill-create-pathcap-example-10124424[m
  pu-sprint-2025-q1-pm5-onesmus-10127834[m
  temp[m
+ [36mtrainee-pr-log[m
