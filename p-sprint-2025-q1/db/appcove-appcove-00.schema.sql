-- AppCove Application Database Schema
-- Basic application schema for demonstration purposes

CREATE SCHEMA IF NOT EXISTS appcove_appcove;

-- Application settings table
CREATE TABLE appcove_appcove.app_setting (
    setting_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    created_by_identity_uuid uuid NOT NULL,
    updated_by_identity_uuid uuid NOT NULL,
    setting_key varchar(255) NOT NULL UNIQUE,
    setting_value text,
    description text,
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT app_setting_pkey PRIMARY KEY (setting_uuid),
    CONSTRAINT app_setting_created_by_fkey FOREIGN KEY (created_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT app_setting_updated_by_fkey FOREIGN KEY (updated_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- User preferences table
CREATE TABLE appcove_appcove.user_preference (
    preference_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    identity_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    preference_key varchar(255) NOT NULL,
    preference_value text,
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT user_preference_pkey PRIMARY KEY (preference_uuid),
    CONSTRAINT user_preference_identity_fkey FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT user_preference_unique UNIQUE (identity_uuid, preference_key)
);

-- Activity log table
CREATE TABLE appcove_appcove.activity_log (
    log_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    identity_uuid uuid,
    create_ts timestamptz NOT NULL DEFAULT now(),
    activity_type varchar(64) NOT NULL,
    activity_description text,
    ip_address inet,
    user_agent text,
    session_token varchar(255),
    CONSTRAINT activity_log_pkey PRIMARY KEY (log_uuid),
    CONSTRAINT activity_log_identity_fkey FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE SET NULL ON UPDATE RESTRICT
);

-- Indexes for better performance
CREATE INDEX idx_app_setting_key ON appcove_appcove.app_setting(setting_key);
CREATE INDEX idx_app_setting_active ON appcove_appcove.app_setting(active);

CREATE INDEX idx_user_preference_identity ON appcove_appcove.user_preference(identity_uuid);
CREATE INDEX idx_user_preference_key ON appcove_appcove.user_preference(preference_key);
CREATE INDEX idx_user_preference_active ON appcove_appcove.user_preference(active);

CREATE INDEX idx_activity_log_identity ON appcove_appcove.activity_log(identity_uuid);
CREATE INDEX idx_activity_log_create_ts ON appcove_appcove.activity_log(create_ts);
CREATE INDEX idx_activity_log_activity_type ON appcove_appcove.activity_log(activity_type);
CREATE INDEX idx_activity_log_session_token ON appcove_appcove.activity_log(session_token);
