-- AppCove Application Initial Data
-- Sample data for demonstration purposes

-- Insert some default application settings
INSERT INTO appcove_appcove.app_setting (
    created_by_identity_uuid,
    updated_by_identity_uuid,
    setting_key,
    setting_value,
    description
) VALUES 
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000',
    'app_name',
    'AppCove Application',
    'The display name of the application'
),
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000',
    'app_version',
    '1.0.0',
    'Current version of the application'
),
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000',
    'maintenance_mode',
    'false',
    'Whether the application is in maintenance mode'
),
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000',
    'max_users',
    '1000',
    'Maximum number of users allowed'
),
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000',
    'welcome_message',
    'Welcome to AppCove! This is a demonstration application built with the AppCove framework.',
    'Welcome message displayed to new users'
);
